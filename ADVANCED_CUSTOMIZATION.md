# دليل التخصيص المتقدم 🎨

## تخصيص الألوان والتدرجات

### تغيير الألوان الأساسية

```css
:root {
    /* الألوان الأساسية */
    --primary-color: #dc3545;
    --primary-dark: #b91c1c;
    --secondary-color: #3b82f6;
    --secondary-dark: #2563eb;
    --success-color: #22c55e;
    --success-dark: #15803d;
    --warning-color: #f59e0b;
    --warning-dark: #d97706;
    --purple-color: #9333ea;
    --purple-dark: #7e22ce;
    
    /* ألوان الخلفية */
    --bg-primary: rgba(15, 23, 42, 0.95);
    --bg-secondary: rgba(30, 41, 59, 0.95);
    --bg-tertiary: rgba(51, 65, 85, 0.8);
}
```

### تخصيص التدرجات

```css
/* تدرج مخصص للعداد */
.player-count {
    background: linear-gradient(135deg, 
        var(--primary-color) 0%, 
        var(--primary-dark) 100%);
}

/* تدرج مخصص للأنشطة المتاحة */
.activity-item[data-status="available"] {
    background: linear-gradient(135deg, 
        var(--success-color) 0%, 
        var(--success-dark) 100%);
}
```

## تخصيص الأيقونات

### إضافة أيقونات جديدة

```javascript
// في script.js
const iconMap = {
    'Post OP': 'fa-duotone fa-truck-fast',
    'Bank Heist': 'fa-duotone fa-building-columns',
    'Drug Deal': 'fa-duotone fa-pills',
    'Car Theft': 'fa-duotone fa-car-side',
    'Gang War': 'fa-duotone fa-gun',
    'default': 'fa-duotone fa-sparkles'
};
```

### تخصيص ألوان الأيقونات

```css
/* أيقونات بألوان مخصصة */
.info-icon i {
    background: linear-gradient(45deg, #your-color-1, #your-color-2);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
```

## تخصيص الانيميشن

### إضافة انيميشن مخصص

```css
@keyframes customPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.custom-animation {
    animation: customPulse 2s infinite;
}
```

### تخصيص سرعة الانيميشن

```css
/* انيميشن سريع */
.fast-animation {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* انيميشن بطيء */
.slow-animation {
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
```

## تخصيص التخطيط

### تغيير مواقع العناصر

```css
/* نقل عداد اللاعبين إلى الوسط العلوي */
.player-count {
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
}

/* نقل لوحة المعلومات إلى اليمين */
.player-info-panel {
    right: 60px;
    left: auto;
}
```

### تخصيص الشبكة

```css
/* شبكة 5 أعمدة للأنشطة */
.activities-grid {
    grid-template-columns: repeat(5, 1fr);
}

/* شبكة عمودية للأنشطة */
.activities-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(auto, 1fr);
}
```

## تخصيص الخطوط

### استخدام خط مختلف

```css
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

body {
    font-family: 'Tajawal', 'Alexandria', sans-serif;
}
```

### تخصيص أحجام الخطوط

```css
/* خطوط أكبر */
.large-text {
    font-size: 1.2em;
    font-weight: 600;
}

/* خطوط أصغر */
.small-text {
    font-size: 0.85em;
    font-weight: 400;
}
```

## تخصيص التأثيرات البصرية

### تأثير الضبابية المتقدم

```css
.advanced-blur {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(15, 23, 42, 0.8);
}
```

### تأثيرات الظلال

```css
/* ظل ناعم */
.soft-shadow {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* ظل قوي */
.strong-shadow {
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 8px 32px rgba(220, 53, 69, 0.3);
}
```

## تخصيص الاستجابة

### نقاط توقف مخصصة

```css
/* للشاشات الكبيرة جداً */
@media (min-width: 1920px) {
    .player-info-panel {
        min-width: 400px;
    }
}

/* للأجهزة اللوحية */
@media (max-width: 768px) {
    .activities-grid {
        grid-template-columns: 1fr;
    }
}
```

## تخصيص الثيمات

### ثيم داكن

```css
.dark-theme {
    --bg-primary: rgba(0, 0, 0, 0.95);
    --bg-secondary: rgba(20, 20, 20, 0.95);
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
}
```

### ثيم ملون

```css
.colorful-theme {
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --success-color: #45b7d1;
    --warning-color: #f9ca24;
}
```

## تخصيص التفاعل

### تأثيرات hover مخصصة

```css
.custom-hover:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    filter: brightness(1.1);
}
```

### تأثيرات النقر

```css
.custom-click:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}
```

## نصائح للأداء

### تحسين الانيميشن

```css
/* استخدم transform بدلاً من تغيير الخصائص الأخرى */
.optimized-animation {
    transform: translateZ(0); /* تفعيل تسريع الأجهزة */
    will-change: transform; /* تحسين الأداء */
}
```

### تقليل إعادة الرسم

```css
/* تجنب تغيير هذه الخصائص في الانيميشن */
/* width, height, padding, margin, border */

/* استخدم هذه بدلاً منها */
/* transform, opacity, filter */
```
