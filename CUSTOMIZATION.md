# دليل التخصيص - WS Player Info

## تخصيص البيانات المعروضة

### 1. تعديل معلومات اللاعب

لتخصيص البيانات التي تظهر في لوحة معلومات اللاعب، عدل في `client/cl_main.lua`:

```lua
function UpdatePlayerData()
    local PlayerData = QBCore.Functions.GetPlayerData()
    if not PlayerData then return end
    
    local playerInfo = {
        nationality = PlayerData.charinfo.nationality,
        birthdate = PlayerData.charinfo.birthdate,
        job = PlayerData.job.label,
        playtime = GetPlayTime(), -- دالة مخصصة
        paycheck = PlayerData.job.payment,
        bank = PlayerData.money.bank,
        cash = PlayerData.money.cash,
        coins = PlayerData.money.crypto,
        insurance = GetInsuranceStatus() -- دالة مخصصة
    }
end
```

### 2. ربط نظام ساعات اللعب

```lua
function GetPlayTime()
    -- مثال للربط مع نظام ساعات اللعب
    local playTime = exports['your-playtime-script']:GetPlayerPlayTime()
    return playTime .. " hours"
end
```

### 3. ربط نظام التأمين

```lua
function GetInsuranceStatus()
    -- مثال للربط مع نظام التأمين
    local hasInsurance = exports['your-insurance-script']:HasInsurance()
    return hasInsurance and "Insured" or "No Insurance"
end
```

## تخصيص الأنشطة

### إضافة نشاط جديد

1. **في config.lua**:
```lua
Activities = {
    ['new-activity'] = { name = 'NEW ACTIVITY', icon = '🎯' },
}
```

2. **في html/index.html**:
```html
<div class="activity-item" data-status="available" id="new-activity">
    <div class="activity-icon">🎯</div>
    <div class="activity-label">NEW ACTIVITY</div>
</div>
```

3. **في server/sv_main.lua**:
```lua
local activities = {
    ['new-activity'] = 'available', -- أو 'busy' أو 'unavailable'
}
```

### تخصيص منطق توفر الأنشطة

```lua
local function IsActivityAvailable(activityName)
    if activityName == 'stores' then
        -- تحقق من عدد الشرطة المطلوب
        local policeCount = GetJobAvailability('police')
        return policeCount >= 3
    elseif activityName == 'houses' then
        -- تحقق من الوقت
        local hour = GetClockHours()
        return hour >= 22 or hour <= 6 -- متاح فقط ليلاً
    end
    
    return true
end
```

## تخصيص البافات

### إضافة نظام بافات مخصص

```lua
QBCore.Functions.CreateCallback('ws-playerinfo:getActiveBuffs', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    local buffs = {}
    
    -- مثال: بف الراتب المضاعف
    if Player.PlayerData.metadata.salaryBoost then
        table.insert(buffs, {
            name = "Salary Boost",
            icon = "💰",
            timeLeft = formatTime(Player.PlayerData.metadata.salaryBoostTime),
            multiplier = "2"
        })
    end
    
    -- مثال: بف الخبرة المضاعفة
    if Player.PlayerData.metadata.xpBoost then
        table.insert(buffs, {
            name = "XP Boost",
            icon = "⭐",
            timeLeft = formatTime(Player.PlayerData.metadata.xpBoostTime),
            multiplier = "1.5"
        })
    end
    
    cb(buffs)
end)
```

## تخصيص التصميم

### تغيير الألوان

في `html/style.css`:

```css
/* تغيير اللون الأساسي */
.player-count,
.panel-header.slt-dev,
.panel-header.details,
.header-tab.active,
.buffs-header {
    background: rgba(34, 139, 34, 0.9); /* أخضر بدلاً من أحمر */
}

/* تغيير لون الأنشطة المتاحة */
.activity-item[data-status="available"] {
    background: rgba(34, 139, 34, 0.9);
}
```

### تغيير المواقع

```css
/* تحريك عداد اللاعبين */
.player-count {
    top: 50px;
    left: 50px;
}

/* تحريك لوحة معلومات اللاعب */
.player-info-panel {
    left: 100px;
}
```

## إعدادات متقدمة

### تخصيص الكاميرا

في `config.lua`:

```lua
CameraSettings = {
    enableCamera = true,
    cameraShake = false,        -- إيقاف اهتزاز الكاميرا
    shakeIntensity = 1.0,       -- تقليل شدة الاهتزاز
    transitionTime = 500,       -- زيادة وقت الانتقال
    freezePlayer = false,       -- عدم تجميد اللاعب
    timecycleModifier = "hud_def_desatcrunch", -- تأثير بصري مختلف
    timecycleIntensity = 0.3
}
```

### تخصيص فترة التحديث

```lua
UISettings = {
    updateInterval = 3000, -- تحديث كل 3 ثوانٍ بدلاً من 5
}
```

## ربط أنظمة خارجية

### ربط نظام العصابات

```lua
function UpdateActivities()
    QBCore.Functions.TriggerCallback('ws-playerinfo:getActivitiesStatus', function(activities)
        -- تحقق من حالة العصابات
        local gangWar = exports['gang-system']:IsGangWarActive()
        if gangWar then
            activities['stores'] = 'unavailable'
            activities['houses'] = 'unavailable'
        end
        
        SendNUIMessage({
            action = "updateActivities",
            activities = activities
        })
    end)
end
```

### ربط نظام الطقس

```lua
function UpdateActivities()
    local weather = GetWeatherTypeTransition()
    
    -- منع بعض الأنشطة في الطقس السيء
    if weather == 'THUNDER' or weather == 'RAIN' then
        activities['jewelary'] = 'unavailable'
    end
end
```
