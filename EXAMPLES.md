# أمثلة للاستخدام المتقدم 🚀

## مثال 1: ربط نظام العصابات

### في server/sv_main.lua

```lua
-- إضافة callback للتحقق من حالة العصابات
QBCore.Functions.CreateCallback('ws-playerinfo:getGangStatus', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    local gangData = {
        inGang = false,
        gangName = nil,
        gangRank = nil,
        territory = nil
    }
    
    if Player.PlayerData.gang and Player.PlayerData.gang.name ~= 'none' then
        gangData.inGang = true
        gangData.gangName = Player.PlayerData.gang.label
        gangData.gangRank = Player.PlayerData.gang.grade.name
        -- التحقق من المنطقة
        gangData.territory = exports['gang-system']:GetPlayerTerritory(source)
    end
    
    cb(gangData)
end)

-- تحديث حالة الأنشطة بناءً على العصابات
local function UpdateActivitiesBasedOnGangs()
    local activities = {
        ['leo-kidnap'] = 'available',
        ['stores'] = 'available',
        ['houses'] = 'available',
        ['jewelary'] = 'available'
    }
    
    -- التحقق من حرب العصابات
    if exports['gang-system']:IsGangWarActive() then
        activities['stores'] = 'unavailable'
        activities['houses'] = 'unavailable'
    end
    
    -- التحقق من عدد الشرطة
    local policeCount = GetJobAvailability('police')
    if policeCount < 3 then
        activities['jewelary'] = 'unavailable'
        activities['leo-kidnap'] = 'unavailable'
    end
    
    return activities
end
```

## مثال 2: نظام البافات المتقدم

### في server/sv_main.lua

```lua
-- نظام بافات ديناميكي
QBCore.Functions.CreateCallback('ws-playerinfo:getActiveBuffs', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    local buffs = {}
    
    -- بف الراتب المضاعف
    if Player.PlayerData.metadata.salaryBoost then
        local timeLeft = Player.PlayerData.metadata.salaryBoostEnd - os.time()
        if timeLeft > 0 then
            table.insert(buffs, {
                name = "Salary Boost",
                icon = "fa-duotone fa-money-check-dollar",
                timeLeft = formatTime(timeLeft),
                multiplier = tostring(Player.PlayerData.metadata.salaryMultiplier)
            })
        end
    end
    
    -- بف الخبرة
    if Player.PlayerData.metadata.xpBoost then
        local timeLeft = Player.PlayerData.metadata.xpBoostEnd - os.time()
        if timeLeft > 0 then
            table.insert(buffs, {
                name = "XP Boost",
                icon = "fa-duotone fa-star",
                timeLeft = formatTime(timeLeft),
                multiplier = "1.5"
            })
        end
    end
    
    -- بف السرعة
    if Player.PlayerData.metadata.speedBoost then
        table.insert(buffs, {
            name = "Speed Boost",
            icon = "fa-duotone fa-gauge-high",
            timeLeft = "∞",
            multiplier = "2"
        })
    end
    
    cb(buffs)
end)

function formatTime(seconds)
    if seconds <= 0 then return "0s" end
    
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    
    if hours > 0 then
        return string.format("%dh %dm", hours, minutes)
    elseif minutes > 0 then
        return string.format("%dm %ds", minutes, secs)
    else
        return string.format("%ds", secs)
    end
end
```

## مثال 3: تكامل مع نظام الطقس

### في client/cl_main.lua

```lua
-- تحديث الأنشطة بناءً على الطقس
function UpdateActivitiesBasedOnWeather()
    local weather = GetWeatherTypeTransition()
    local hour = GetClockHours()
    
    QBCore.Functions.TriggerCallback('ws-playerinfo:getActivitiesStatus', function(activities)
        -- منع بعض الأنشطة في الطقس السيء
        if weather == 'THUNDER' or weather == 'RAIN' then
            activities['jewelary'] = 'unavailable'
            activities['stores'] = 'busy'
        end
        
        -- أنشطة ليلية فقط
        if hour >= 22 or hour <= 6 then
            activities['houses'] = 'available'
        else
            activities['houses'] = 'unavailable'
        end
        
        -- أنشطة نهارية فقط
        if hour >= 8 and hour <= 18 then
            activities['exchange'] = 'available'
        else
            activities['exchange'] = 'unavailable'
        end
        
        SendNUIMessage({
            action = "updateActivities",
            activities = activities
        })
    end)
end
```

## مثال 4: نظام الإشعارات

### في html/script.js

```javascript
// إضافة نظام إشعارات
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fa-duotone fa-bell"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // انيميشن الدخول
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // إزالة الإشعار
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, duration);
}

// استخدام الإشعارات
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'showNotification':
            showNotification(data.message, data.type, data.duration);
            break;
        case 'activityStatusChanged':
            showNotification(`Activity ${data.activity} is now ${data.status}`, 'info');
            break;
    }
});
```

## مثال 5: حفظ الإعدادات

### في client/cl_main.lua

```lua
-- حفظ إعدادات اللاعب
function SavePlayerSettings(settings)
    TriggerServerEvent('ws-playerinfo:saveSettings', settings)
end

function LoadPlayerSettings()
    QBCore.Functions.TriggerCallback('ws-playerinfo:getSettings', function(settings)
        if settings then
            SendNUIMessage({
                action = "loadSettings",
                settings = settings
            })
        end
    end)
end

-- تحديث الإعدادات عند تغيير التبويب
RegisterNUICallback('settingsChanged', function(data, cb)
    SavePlayerSettings(data.settings)
    cb('ok')
end)
```

### في server/sv_main.lua

```lua
-- حفظ الإعدادات في قاعدة البيانات
RegisterServerEvent('ws-playerinfo:saveSettings')
AddEventHandler('ws-playerinfo:saveSettings', function(settings)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if Player then
        MySQL.Async.execute('UPDATE players SET playerinfo_settings = ? WHERE citizenid = ?', {
            json.encode(settings),
            Player.PlayerData.citizenid
        })
    end
end)

QBCore.Functions.CreateCallback('ws-playerinfo:getSettings', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    
    if Player then
        MySQL.Async.fetchScalar('SELECT playerinfo_settings FROM players WHERE citizenid = ?', {
            Player.PlayerData.citizenid
        }, function(result)
            if result then
                cb(json.decode(result))
            else
                cb(nil)
            end
        end)
    else
        cb(nil)
    end
end)
```

## مثال 6: تكامل مع Discord

### في server/sv_main.lua

```lua
-- إرسال إحصائيات إلى Discord
function SendStatsToDiscord()
    local playerCount = #GetPlayers()
    local activities = GetActivitiesStatus()
    
    local embed = {
        title = "Server Statistics",
        color = 15158332, -- أحمر
        fields = {
            {
                name = "Players Online",
                value = tostring(playerCount),
                inline = true
            },
            {
                name = "Available Activities",
                value = GetAvailableActivitiesCount(activities),
                inline = true
            }
        },
        timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
    }
    
    -- إرسال إلى webhook
    PerformHttpRequest('YOUR_DISCORD_WEBHOOK', function(err, text, headers) end, 'POST', json.encode({
        embeds = {embed}
    }), {['Content-Type'] = 'application/json'})
end

-- إرسال الإحصائيات كل 30 دقيقة
CreateThread(function()
    while true do
        Wait(1800000) -- 30 دقيقة
        SendStatsToDiscord()
    end
end)
```
