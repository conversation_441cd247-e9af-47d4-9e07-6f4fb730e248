# WS Player Info Script

سكريبت لعرض معلومات اللاعب والأنشطة في FiveM باستخدام QBCore

## المميزات

- **عرض معلومات اللاعب**: الجنسية، تاريخ الميلاد، الوظيفة، ساعات اللعب، الراتب، البنك، النقد، العملات، التأمين
- **عداد اللاعبين**: يظهر عدد اللاعبين المتصلين في السيرفر
- **حالة الأنشطة**: يظهر حالة السرقات والقطاعات (متاح، مشغول، غير متاح)
- **البافات النشطة**: يظهر البافات الحالية مع الوقت المتبقي والمضاعف
- **كاميرا خاصة**: كاميرا سينمائية عند عرض المعلومات

## التحكم

- **مفتاح P**: لإظهار/إخفاء واجهة معلومات اللاعب

## التثبيت

1. ضع المجلد في مجلد resources
2. أضف `ensure ws-playerinfo` في server.cfg
3. أعد تشغيل السيرفر

## التخصيص

### تعديل الإعدادات

يمكنك تعديل الإعدادات في ملف `config.lua`:

```lua
Shared = {
    UISettings = {
        showPlayerCount = true,     -- إظهار عداد اللاعبين
        showPlayerInfo = true,      -- إظهار معلومات اللاعب
        showActivities = true,      -- إظهار الأنشطة
        showBuffs = true,          -- إظهار البافات
        updateInterval = 5000,     -- فترة التحديث بالميلي ثانية
    }
}
```

### إضافة أنشطة جديدة

لإضافة أنشطة جديدة، عدل في `config.lua`:

```lua
Activities = {
    ['activity-name'] = { name = 'Activity Display Name', icon = '🎯' },
}
```

ثم أضف العنصر في `html/index.html`:

```html
<div class="activity-item" data-status="available" id="activity-name">
    <div class="activity-icon">🎯</div>
    <div class="activity-label">ACTIVITY NAME</div>
</div>
```

### تخصيص البافات

لتخصيص نظام البافات، عدل في `server/sv_main.lua` في دالة `ws-playerinfo:getActiveBuffs`:

```lua
local buffs = {
    {
        name = "اسم البف",
        icon = "🎯",
        timeLeft = "10m",
        multiplier = "2"
    }
}
```

## حالات الأنشطة

- **أحمر (available)**: النشاط متاح
- **برتقالي (busy)**: النشاط مشغول
- **أسود (unavailable)**: النشاط غير متاح

## التكامل مع أنظمة أخرى

### نظام ساعات اللعب

لربط نظام ساعات اللعب الخاص بك، عدل دالة `GetPlayTime()` في `client/cl_main.lua`:

```lua
function GetPlayTime()
    -- استبدل هذا بنظام ساعات اللعب الخاص بك
    return "106 hours"
end
```

### نظام التأمين

لربط نظام التأمين الخاص بك، عدل دالة `GetInsuranceStatus()` في `client/cl_main.lua`:

```lua
function GetInsuranceStatus()
    -- استبدل هذا بنظام التأمين الخاص بك
    return "No Insurance"
end
```

## المتطلبات

- QBCore Framework
- FiveM Server

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى التواصل معنا.

## الترخيص

هذا السكريبت مجاني للاستخدام والتعديل.
