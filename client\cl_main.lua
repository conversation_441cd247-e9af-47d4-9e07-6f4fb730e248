local QBCore = exports["qb-core"]:GetCoreObject()
local isRunning = false
local cam = nil
local isUIVisible = false

CreateThread(function() 
    while true do
        Wait(0)
        DisableControlAction(0, 199, true) -- Controlkey "P"
    end
end)

local function IsGameplayCamForwardOrBackward(playerPed)
    local playerHeading = GetEntityHeading(playerPed)    
    local camRot = GetGameplayCamRot(2)
    local camHeading = camRot.z
    local angleDiff = math.abs(playerHeading - camHeading)
    if angleDiff > 90 and angleDiff < 270 then
        return true --"Camera is Backward"
    else
        return false --"Camera is Forward"
    end
end
--[[
damage or Drone_FishEye_Lens or vehicle_subint
crane_cam 
DeadlineNeon01 
dont_tazeme_bro_b 
Hint_cam 
hud_def_desatcrunch
player_transition
PlayerSwitchPulse
MP_cornoa_heist_BW_night or MP_cornoa_heist_night or MP_job_end_night or Heist4CameraFlash
MP_Heist_Tuner_SmShop_Americana or MP_casino_apartment_changing or Mp_Heist3_loading_MainRoom or Mp_Heist3_Loading_Stairs
mp_lad_judgment
Bikers FRANKLIN
]]
local function SLTCam(boolean)
    if not Shared.CameraSettings.enableCamera then return end
    if not IsPedOnFoot(PlayerPedId()) or IsPedJumping(PlayerPedId()) or IsPedFalling(PlayerPedId()) or IsPedInMeleeCombat(PlayerPedId()) or IsPedRagdoll(PlayerPedId()) then return end
    if GetFollowPedCamViewMode() == 4 then return end
    --if IsPedInAnyVehicle(PlayerPedId(), true) then return end
    local playerPed = PlayerPedId()
    if boolean then
        if not cam then
            cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
            SetCamActive(cam, true)
            if Shared.CameraSettings.cameraShake then
                ShakeCam(cam, "HAND_SHAKE", Shared.CameraSettings.shakeIntensity)
            end
            RenderScriptCams(true, true, Shared.CameraSettings.transitionTime, true, true)
            SetTransitionTimecycleModifier(Shared.CameraSettings.timecycleModifier, Shared.CameraSettings.timecycleIntensity)
            if Shared.CameraSettings.freezePlayer then
                FreezeEntityPosition(playerPed, true)
            end
        end
        isRunning = true
        CreateThread(function()
            while isRunning do
                DisableAllControlActions(0)
                local coordsBone = GetPedBoneCoords(PlayerPedId(), 24818, 0.0, 0.0, 0.0)
                local coords = GetEntityCoords(playerPed)
                if IsGameplayCamForwardOrBackward(playerPed) == false then
                    AttachCamToEntity(cam, playerPed, 0.0, -1.7, 0.5, true)
                    PointCamAtPedBone(cam, playerPed, 24818, 1.0, 0.0, 0.0, true)
                elseif IsGameplayCamForwardOrBackward(playerPed) == true then
                    AttachCamToEntity(cam, playerPed, 0.0, 1.7, 0.5, true)
                    PointCamAtPedBone(cam, playerPed, 24818, -1.0, 0.0, 0.0, true)
                end
                Wait(0)
            end
        end)
    else
        if cam then
            SetTransitionTimecycleModifier("default", 0.55)
            RenderScriptCams(false, true, Shared.CameraSettings.transitionTime, true, true)
            DestroyCam(cam, false)
            if Shared.CameraSettings.freezePlayer then
                FreezeEntityPosition(playerPed, false)
            end
            cam = nil
        end
        isRunning = false
    end
end

-- UI Functions
local function ShowPlayerInfoUI()
    if not isUIVisible then
        SetNuiFocus(false, false)
        SendNUIMessage({
            action = "showUI"
        })
        isUIVisible = true

        -- Start data update loop
        CreateThread(function()
            while isUIVisible do
                UpdatePlayerData()
                UpdatePlayerCount()
                UpdateActivities()
                UpdateBuffs()
                Wait(Shared.UISettings.updateInterval or 5000)
            end
        end)
    end
end

local function HidePlayerInfoUI()
    if isUIVisible then
        SendNUIMessage({
            action = "hideUI"
        })
        isUIVisible = false
    end
end

function UpdatePlayerData()
    local PlayerData = QBCore.Functions.GetPlayerData()
    if not PlayerData then return end

    local playerInfo = {
        nationality = PlayerData.charinfo and PlayerData.charinfo.nationality or Shared.DefaultPlayerData.nationality,
        birthdate = PlayerData.charinfo and PlayerData.charinfo.birthdate or Shared.DefaultPlayerData.birthdate,
        job = PlayerData.job and PlayerData.job.label or Shared.DefaultPlayerData.job,
        playtime = GetPlayTime(),
        paycheck = PlayerData.job and PlayerData.job.payment or Shared.DefaultPlayerData.paycheck,
        bank = PlayerData.money and PlayerData.money.bank or Shared.DefaultPlayerData.bank,
        cash = PlayerData.money and PlayerData.money.cash or Shared.DefaultPlayerData.cash,
        coins = PlayerData.money and PlayerData.money.crypto or Shared.DefaultPlayerData.coins,
        insurance = GetInsuranceStatus()
    }

    SendNUIMessage({
        action = "updatePlayerInfo",
        playerData = playerInfo
    })
end

function UpdatePlayerCount()
    QBCore.Functions.TriggerCallback('ws-playerinfo:getPlayerCount', function(count)
        SendNUIMessage({
            action = "updatePlayerCount",
            count = count
        })
    end)
end

function UpdateActivities()
    QBCore.Functions.TriggerCallback('ws-playerinfo:getActivitiesStatus', function(activities)
        SendNUIMessage({
            action = "updateActivities",
            activities = activities
        })
    end)
end

function UpdateBuffs()
    QBCore.Functions.TriggerCallback('ws-playerinfo:getActiveBuffs', function(buffs)
        SendNUIMessage({
            action = "updateBuffs",
            buffs = buffs
        })
    end)
end

function GetPlayTime()
    -- This should be implemented based on your server's playtime system
    -- For now, returning a placeholder
    return "106 hours"
end

function GetInsuranceStatus()
    -- This should be implemented based on your server's insurance system
    -- For now, returning a placeholder
    return "No Insurance"
end

RegisterCommand("+cra", function()
    SLTCam(true)
    ShowPlayerInfoUI()
end)
RegisterCommand("-cra", function()
    SLTCam(false)
    HidePlayerInfoUI()
end)
RegisterKeyMapping('+cra', "Show Player Info", 'keyboard', "P")

-- NUI Callbacks
RegisterNUICallback('requestPlayerData', function(data, cb)
    UpdatePlayerData()
    cb('ok')
end)

RegisterNUICallback('requestAvailabilityData', function(data, cb)
    UpdateActivities()
    cb('ok')
end)

RegisterNUICallback('requestActivitiesData', function(data, cb)
    UpdateActivities()
    cb('ok')
end)