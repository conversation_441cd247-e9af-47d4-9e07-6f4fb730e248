local QBCore = exports["qb-core"]:GetCoreObject()
local isRunning = false
local cam = nil

CreateThread(function() 
    while true do
        Wait(0)
        DisableControlAction(0, 199, true) -- Controlkey "P"
    end
end)

local function IsGameplayCamForwardOrBackward(playerPed)
    local playerHeading = GetEntityHeading(playerPed)    
    local camRot = GetGameplayCamRot(2)
    local camHeading = camRot.z
    local angleDiff = math.abs(playerHeading - camHeading)
    if angleDiff > 90 and angleDiff < 270 then
        return true --"Camera is Backward"
    else
        return false --"Camera is Forward"
    end
end
--[[
damage or Drone_FishEye_Lens or vehicle_subint
crane_cam 
DeadlineNeon01 
dont_tazeme_bro_b 
Hint_cam 
hud_def_desatcrunch
player_transition
PlayerSwitchPulse
MP_cornoa_heist_BW_night or MP_cornoa_heist_night or MP_job_end_night or Heist4CameraFlash
MP_Heist_Tuner_SmShop_Americana or MP_casino_apartment_changing or Mp_Heist3_loading_MainRoom or Mp_Heist3_Loading_Stairs
mp_lad_judgment
Bikers FRANKLIN
]]
local function SLTCam(boolean)
    if not IsPedOnFoot(PlayerPedId()) or IsPedJumping(PlayerPedId()) or IsPedFalling(PlayerPedId()) or IsPedInMeleeCombat(PlayerPedId()) or IsPedRagdoll(PlayerPedId()) then return end
    if GetFollowPedCamViewMode() == 4 then return end
    --if IsPedInAnyVehicle(PlayerPedId(), true) then return end
    local playerPed = PlayerPedId()
    if boolean then
        if not cam then
            cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
            SetCamActive(cam, true)
            ShakeCam(cam, "HAND_SHAKE", 2.0)
            RenderScriptCams(true, true, 200, true, true)
            -- MP_job_end_night
            SetTransitionTimecycleModifier("FRANKLIN", 0.45)
            FreezeEntityPosition(playerPed, true)
        end
        isRunning = true
        CreateThread(function()
            while isRunning do
                DisableAllControlActions(0)
                local coordsBone = GetPedBoneCoords(PlayerPedId(), 24818, 0.0, 0.0, 0.0)
                local coords = GetEntityCoords(playerPed)
                if IsGameplayCamForwardOrBackward(playerPed) == false then
                    AttachCamToEntity(cam, playerPed, 0.0, -1.7, 0.5, true)
                    PointCamAtPedBone(cam, playerPed, 24818, 1.0, 0.0, 0.0, true)
                elseif IsGameplayCamForwardOrBackward(playerPed) == true then
                    AttachCamToEntity(cam, playerPed, 0.0, 1.7, 0.5, true)
                    PointCamAtPedBone(cam, playerPed, 24818, -1.0, 0.0, 0.0, true)
                end
                Wait(0)
            end
        end)
    else
        if cam then
            SetTransitionTimecycleModifier("default", 0.55)
            RenderScriptCams(false, true, 200, true, true)
            DestroyCam(cam, false)
            FreezeEntityPosition(playerPed, false)
            cam = nil
        end
        isRunning = false
    end
end

RegisterCommand("+cra", function()
    SLTCam(true)
end)
RegisterCommand("-cra", function()
    SLTCam(false)
end)
RegisterKeyMapping('+cra', "Show Player Info", 'keyboard', "P")