Shared = {
    -- UI Settings
    UISettings = {
        showPlayerCount = true,
        showPlayerInfo = true,
        showActivities = true,
        showBuffs = true,
        updateInterval = 5000, -- Update every 5 seconds
    },

    -- Activities Configuration
    Activities = {
        ['leo-kidnap'] = { name = 'LEO KIDNAP', icon = '🚔' },
        ['stores'] = { name = 'STORES', icon = '🏪' },
        ['police'] = { name = 'POLICE', icon = '🛡️' },
        ['sheriff'] = { name = 'SHERIFF', icon = '⭐' },
        ['houses'] = { name = 'HOUSES', icon = '🏠' },
        ['exchange'] = { name = 'EXCHANGE', icon = '💱' },
        ['hospital'] = { name = 'HOSPITAL', icon = '❤️' },
        ['jewelary'] = { name = 'JEWELARY', icon = '💎' },
    },

    -- Default Player Data
    DefaultPlayerData = {
        nationality = 'USA',
        birthdate = '2000/1/1',
        job = 'Unemployed',
        playtime = '0 hours',
        paycheck = 0,
        bank = 0,
        cash = 0,
        coins = 0,
        insurance = 'No Insurance'
    },

    -- Camera Settings
    CameraSettings = {
        enableCamera = true,        -- Enable/disable camera when showing UI
        cameraShake = true,         -- Enable camera shake effect
        shakeIntensity = 2.0,       -- Shake intensity (0.0 - 5.0)
        transitionTime = 200,       -- Camera transition time in ms
        freezePlayer = true,        -- Freeze player when camera is active
        timecycleModifier = "FRANKLIN", -- Visual effect modifier
        timecycleIntensity = 0.45   -- Effect intensity (0.0 - 1.0)
    },

    -- UI Positioning
    UIPositions = {
        playerCount = { top = 20, left = 20 },
        playerInfo = { left = 50, centerVertical = true },
        activities = { top = 20, right = 20 },
        buffs = { bottom = 20, right = 20 }
    }
}