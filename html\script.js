let isUIVisible = false;

// Listen for messages from Lua
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'showUI':
            showPlayerInfoUI();
            break;
        case 'hideUI':
            hidePlayerInfoUI();
            break;
        case 'updatePlayerInfo':
            updatePlayerInfo(data.playerData);
            break;
        case 'updatePlayerCount':
            updatePlayerCount(data.count);
            break;
        case 'updateActivities':
            updateActivities(data.activities);
            break;
        case 'updateBuffs':
            updateBuffs(data.buffs);
            break;
    }
});

function showPlayerInfoUI() {
    const ui = document.getElementById('playerInfoUI');
    ui.classList.remove('hidden');
    isUIVisible = true;
    
    // Request initial data
    fetch(`https://${GetParentResourceName()}/requestPlayerData`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

function hidePlayerInfoUI() {
    const ui = document.getElementById('playerInfoUI');
    ui.classList.add('hidden');
    isUIVisible = false;
}

function updatePlayerInfo(playerData) {
    if (!playerData) return;
    
    // Update player info
    document.getElementById('nationality').textContent = playerData.nationality || 'USA';
    document.getElementById('birthdate').textContent = playerData.birthdate || '2000/1/1';
    document.getElementById('job').textContent = playerData.job || 'Unemployed';
    document.getElementById('playtime').textContent = playerData.playtime || '0 hours';
    document.getElementById('paycheck').textContent = `$${playerData.paycheck || 0}`;
    document.getElementById('bank').textContent = `$${playerData.bank || 0}`;
    document.getElementById('cash').textContent = `$${playerData.cash || 0}`;
    document.getElementById('coins').textContent = playerData.coins || 0;
    document.getElementById('insurance').textContent = playerData.insurance || 'No Insurance';
}

function updatePlayerCount(count) {
    document.getElementById('playerCount').textContent = count || 0;
}

function updateActivities(activities) {
    if (!activities) return;
    
    // Update activity statuses
    Object.keys(activities).forEach(activityId => {
        const element = document.getElementById(activityId);
        if (element) {
            element.setAttribute('data-status', activities[activityId]);
        }
    });
}

function updateBuffs(buffs) {
    if (!buffs || !Array.isArray(buffs)) return;

    const buffsList = document.getElementById('buffsList');
    buffsList.innerHTML = '';

    buffs.forEach(buff => {
        const buffItem = document.createElement('div');
        buffItem.className = 'buff-item';

        // Map buff types to Font Awesome icons
        const iconMap = {
            'Post OP': 'fa-duotone fa-truck-fast',
            'Uncle Damby\'s Farms': 'fa-duotone fa-wheat',
            'Salary Boost': 'fa-duotone fa-money-check-dollar',
            'XP Boost': 'fa-duotone fa-star',
            'Speed Boost': 'fa-duotone fa-gauge-high',
            'Health Boost': 'fa-duotone fa-heart-pulse',
            'Armor Boost': 'fa-duotone fa-shield',
            'default': 'fa-duotone fa-sparkles'
        };

        const iconClass = iconMap[buff.name] || iconMap['default'];

        buffItem.innerHTML = `
            <div class="buff-icon">
                <i class="${iconClass}"></i>
            </div>
            <div class="buff-info">
                <div class="buff-name">${buff.name}</div>
                <div class="buff-details">
                    <span class="buff-time">
                        <i class="fa-duotone fa-clock"></i>
                        ${buff.timeLeft}
                    </span>
                    <span class="buff-multiplier">${buff.multiplier}x</span>
                </div>
            </div>
        `;

        buffsList.appendChild(buffItem);
    });
}

// Format time function
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else {
        return `${minutes}m`;
    }
}

// Add loading states and animations
function addLoadingState(element) {
    element.classList.add('loading');
}

function removeLoadingState(element) {
    element.classList.remove('loading');
}

// Enhanced activity status updates with animations
function updateActivityStatus(activityId, status) {
    const element = document.getElementById(activityId);
    if (element) {
        // Add transition effect
        element.style.transform = 'scale(0.95)';
        element.style.opacity = '0.7';

        setTimeout(() => {
            element.setAttribute('data-status', status);
            element.style.transform = 'scale(1)';
            element.style.opacity = '1';
        }, 150);
    }
}

// Activity tab switching with enhanced animations
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.header-tab');
    const activitiesGrid = document.querySelector('.activities-grid');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Don't do anything if already active
            if (this.classList.contains('active')) return;

            // Add loading state
            addLoadingState(activitiesGrid);

            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Get tab text content (remove icon)
            const tabText = this.querySelector('span').textContent;

            // Animate grid transition
            activitiesGrid.style.opacity = '0.5';
            activitiesGrid.style.transform = 'translateY(10px)';

            setTimeout(() => {
                if (tabText === 'AVAILABILITY') {
                    // Show availability view
                    fetch(`https://${GetParentResourceName()}/requestAvailabilityData`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({})
                    }).finally(() => {
                        removeLoadingState(activitiesGrid);
                        activitiesGrid.style.opacity = '1';
                        activitiesGrid.style.transform = 'translateY(0)';
                    });
                } else {
                    // Show activities view
                    fetch(`https://${GetParentResourceName()}/requestActivitiesData`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({})
                    }).finally(() => {
                        removeLoadingState(activitiesGrid);
                        activitiesGrid.style.opacity = '1';
                        activitiesGrid.style.transform = 'translateY(0)';
                    });
                }
            }, 200);
        });
    });

    // Add hover effects to activity items
    const activityItems = document.querySelectorAll('.activity-item');
    activityItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// Utility function to get resource name
function GetParentResourceName() {
    return window.location.hostname;
}
