let isUIVisible = false;

// Listen for messages from Lua
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'showUI':
            showPlayerInfoUI();
            break;
        case 'hideUI':
            hidePlayerInfoUI();
            break;
        case 'updatePlayerInfo':
            updatePlayerInfo(data.playerData);
            break;
        case 'updatePlayerCount':
            updatePlayerCount(data.count);
            break;
        case 'updateActivities':
            updateActivities(data.activities);
            break;
        case 'updateBuffs':
            updateBuffs(data.buffs);
            break;
    }
});

function showPlayerInfoUI() {
    const ui = document.getElementById('playerInfoUI');
    ui.classList.remove('hidden');
    isUIVisible = true;
    
    // Request initial data
    fetch(`https://${GetParentResourceName()}/requestPlayerData`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

function hidePlayerInfoUI() {
    const ui = document.getElementById('playerInfoUI');
    ui.classList.add('hidden');
    isUIVisible = false;
}

function updatePlayerInfo(playerData) {
    if (!playerData) return;
    
    // Update player info
    document.getElementById('nationality').textContent = playerData.nationality || 'USA';
    document.getElementById('birthdate').textContent = playerData.birthdate || '2000/1/1';
    document.getElementById('job').textContent = playerData.job || 'Unemployed';
    document.getElementById('playtime').textContent = playerData.playtime || '0 hours';
    document.getElementById('paycheck').textContent = `$${playerData.paycheck || 0}`;
    document.getElementById('bank').textContent = `$${playerData.bank || 0}`;
    document.getElementById('cash').textContent = `$${playerData.cash || 0}`;
    document.getElementById('coins').textContent = playerData.coins || 0;
    document.getElementById('insurance').textContent = playerData.insurance || 'No Insurance';
}

function updatePlayerCount(count) {
    document.getElementById('playerCount').textContent = count || 0;
}

function updateActivities(activities) {
    if (!activities) return;
    
    // Update activity statuses
    Object.keys(activities).forEach(activityId => {
        const element = document.getElementById(activityId);
        if (element) {
            element.setAttribute('data-status', activities[activityId]);
        }
    });
}

function updateBuffs(buffs) {
    if (!buffs || !Array.isArray(buffs)) return;
    
    const buffsList = document.getElementById('buffsList');
    buffsList.innerHTML = '';
    
    buffs.forEach(buff => {
        const buffItem = document.createElement('div');
        buffItem.className = 'buff-item';
        
        buffItem.innerHTML = `
            <div class="buff-icon">${buff.icon || '📦'}</div>
            <div class="buff-info">
                <div class="buff-name">${buff.name}</div>
                <div class="buff-details">
                    <span class="buff-time">${buff.timeLeft}</span>
                    <span class="buff-multiplier">${buff.multiplier}x</span>
                </div>
            </div>
        `;
        
        buffsList.appendChild(buffItem);
    });
}

// Format time function
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else {
        return `${minutes}m`;
    }
}

// Activity tab switching
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.header-tab');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Here you can add logic to switch between activities and availability views
            if (this.textContent === 'AVAILABILITY') {
                // Show availability view
                fetch(`https://${GetParentResourceName()}/requestAvailabilityData`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });
            } else {
                // Show activities view
                fetch(`https://${GetParentResourceName()}/requestActivitiesData`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });
            }
        });
    });
});

// Utility function to get resource name
function GetParentResourceName() {
    return window.location.hostname;
}
