@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: transparent;
    color: white;
    overflow: hidden;
    user-select: none;
}

.hidden {
    display: none !important;
}

#playerInfoUI {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
}

/* Player Count */
.player-count {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    background: rgba(220, 53, 69, 0.95);
    padding: 10px 18px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.count-icon {
    margin-left: 10px;
    font-size: 22px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.count-number {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Player Info Panel */
.player-info-panel {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    padding: 0;
    min-width: 300px;
    border: 2px solid rgba(220, 53, 69, 0.8);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
}

.panel-header {
    padding: 12px 20px;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    color: white;
}

.panel-header.slt-dev {
    background: rgba(220, 53, 69, 0.9);
    border-radius: 6px 6px 0 0;
}

.panel-header.details {
    background: rgba(220, 53, 69, 0.9);
    margin-top: 1px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 20px;
    background: rgba(40, 40, 40, 0.9);
    border-bottom: 1px solid rgba(60, 60, 60, 0.5);
    font-size: 13px;
}

.info-item:last-child {
    border-bottom: none;
    border-radius: 0 0 6px 6px;
}

.info-label {
    color: #ccc;
}

.info-value {
    color: white;
    font-weight: 500;
}

/* Activities Panel */
.activities-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    overflow: hidden;
    min-width: 320px;
}

.activities-header {
    display: flex;
}

.header-tab {
    flex: 1;
    padding: 12px;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
    background: rgba(60, 60, 60, 0.9);
    cursor: pointer;
    transition: background 0.3s;
}

.header-tab.active {
    background: rgba(220, 53, 69, 0.9);
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;
    padding: 2px;
    background: rgba(40, 40, 40, 0.9);
}

.activity-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 8px;
    background: rgba(60, 60, 60, 0.9);
    transition: all 0.3s;
    cursor: pointer;
    min-height: 80px;
    justify-content: center;
}

.activity-item[data-status="available"] {
    background: rgba(220, 53, 69, 0.9);
}

.activity-item[data-status="busy"] {
    background: rgba(255, 152, 0, 0.9);
}

.activity-item[data-status="unavailable"] {
    background: rgba(60, 60, 60, 0.9);
}

.activity-icon {
    font-size: 20px;
    margin-bottom: 5px;
}

.activity-label {
    font-size: 10px;
    font-weight: bold;
    text-align: center;
    line-height: 1.2;
}

/* Active Buffs Panel */
.buffs-panel {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    overflow: hidden;
    min-width: 320px;
}

.buffs-header {
    padding: 12px 20px;
    background: rgba(220, 53, 69, 0.9);
    font-weight: bold;
    font-size: 12px;
    text-align: center;
    color: white;
}

.buffs-list {
    padding: 10px;
}

.buff-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: rgba(60, 60, 60, 0.9);
    border-radius: 6px;
    margin-bottom: 8px;
}

.buff-item:last-child {
    margin-bottom: 0;
}

.buff-icon {
    font-size: 24px;
    margin-left: 12px;
    width: 40px;
    text-align: center;
}

.buff-info {
    flex: 1;
}

.buff-name {
    font-size: 13px;
    font-weight: 500;
    color: white;
    margin-bottom: 2px;
}

.buff-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.buff-time {
    font-size: 12px;
    color: #ccc;
}

.buff-multiplier {
    font-size: 12px;
    font-weight: bold;
    color: #ff6b6b;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .activities-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .activities-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .player-info-panel {
        left: 20px;
    }
    
    .activities-panel,
    .buffs-panel {
        right: 10px;
        min-width: 280px;
    }
}
