/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Alexandria', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: white;
    overflow: hidden;
    user-select: none;
    font-weight: 400;
    line-height: 1.5;
}

.hidden {
    display: none !important;
}

#playerInfoUI {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    font-size: 14px;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* ===== PLAYER COUNT ===== */
.player-count {
    position: absolute;
    top: 24px;
    left: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.95) 0%, rgba(185, 28, 28, 0.95) 100%);
    padding: 16px 24px;
    border-radius: 16px;
    font-size: 18px;
    font-weight: 600;
    box-shadow:
        0 8px 32px rgba(220, 53, 69, 0.3),
        0 4px 16px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    animation: fadeInUp 0.6s ease-out;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.player-count:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(220, 53, 69, 0.4),
        0 6px 20px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.count-icon {
    font-size: 24px;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    animation: pulse 2s infinite;
}

.count-number {
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* ===== PLAYER INFO PANEL ===== */
.player-info-panel {
    position: absolute;
    top: 50%;
    left: 60px;
    transform: translateY(-50%);
    background: linear-gradient(145deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
    border-radius: 20px;
    padding: 0;
    min-width: 340px;
    border: 1px solid rgba(220, 53, 69, 0.6);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 8px 32px rgba(220, 53, 69, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    animation: fadeInUp 0.8s ease-out 0.2s both;
    overflow: hidden;
}

.panel-header {
    padding: 20px 24px;
    font-weight: 700;
    font-size: 16px;
    text-align: center;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(185, 28, 28, 0.9) 100%);
    position: relative;
    overflow: hidden;
}

.panel-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

.panel-header.slt-dev {
    border-radius: 20px 20px 0 0;
}

.panel-header.details {
    margin-top: 2px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%);
}

.panel-header i {
    font-size: 18px;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    background: rgba(30, 41, 59, 0.6);
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
}

.info-item:hover {
    background: rgba(51, 65, 85, 0.8);
    transform: translateX(4px);
}

.info-item:last-child {
    border-bottom: none;
    border-radius: 0 0 20px 20px;
}

.info-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(185, 28, 28, 0.2) 100%);
    border-radius: 12px;
    margin-left: 16px;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.info-icon i {
    font-size: 18px;
    background: linear-gradient(45deg, #dc3545, #b91c1c);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.info-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    color: white;
    font-weight: 600;
    font-size: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* ===== ACTIVITIES PANEL ===== */
.activities-panel {
    position: absolute;
    top: 24px;
    right: 24px;
    background: linear-gradient(145deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
    border-radius: 20px;
    overflow: hidden;
    min-width: 360px;
    border: 1px solid rgba(220, 53, 69, 0.6);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 8px 32px rgba(220, 53, 69, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.activities-header {
    display: flex;
    background: rgba(30, 41, 59, 0.8);
}

.header-tab {
    flex: 1;
    padding: 16px 20px;
    text-align: center;
    font-weight: 600;
    font-size: 13px;
    background: rgba(71, 85, 105, 0.6);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.header-tab::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc3545, #b91c1c);
    transition: width 0.3s ease;
}

.header-tab:hover {
    background: rgba(71, 85, 105, 0.8);
    transform: translateY(-1px);
}

.header-tab.active {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(185, 28, 28, 0.9) 100%);
    color: white;
}

.header-tab.active::before {
    width: 100%;
}

.header-tab i {
    font-size: 14px;
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 3px;
    padding: 3px;
    background: rgba(30, 41, 59, 0.8);
}

.activity-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 12px;
    background: rgba(71, 85, 105, 0.6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    min-height: 90px;
    border-radius: 12px;
    border: 1px solid rgba(71, 85, 105, 0.3);
    position: relative;
    overflow: hidden;
}

.activity-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: transparent;
    transition: background 0.3s ease;
}

.activity-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.activity-item[data-status="available"] {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(21, 128, 61, 0.8) 100%);
    border-color: rgba(34, 197, 94, 0.6);
}

.activity-item[data-status="available"]::before {
    background: linear-gradient(90deg, #22c55e, #15803d);
}

.activity-item[data-status="busy"] {
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.8) 0%, rgba(194, 65, 12, 0.8) 100%);
    border-color: rgba(249, 115, 22, 0.6);
}

.activity-item[data-status="busy"]::before {
    background: linear-gradient(90deg, #f97316, #c2410c);
}

.activity-item[data-status="unavailable"] {
    background: rgba(71, 85, 105, 0.6);
    border-color: rgba(71, 85, 105, 0.3);
    opacity: 0.6;
}

.activity-icon {
    font-size: 24px;
    margin-bottom: 8px;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.activity-label {
    font-size: 11px;
    font-weight: 600;
    text-align: center;
    line-height: 1.3;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.3px;
}

/* ===== ACTIVE BUFFS PANEL ===== */
.buffs-panel {
    position: absolute;
    bottom: 24px;
    right: 24px;
    background: linear-gradient(145deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
    border-radius: 20px;
    overflow: hidden;
    min-width: 360px;
    border: 1px solid rgba(220, 53, 69, 0.6);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 8px 32px rgba(220, 53, 69, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.buffs-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.9) 0%, rgba(126, 34, 206, 0.9) 100%);
    font-weight: 700;
    font-size: 16px;
    text-align: center;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.buffs-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

.buffs-header i {
    font-size: 18px;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.buffs-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.buff-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
    border-radius: 16px;
    border: 1px solid rgba(71, 85, 105, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.buff-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #9333ea, #7e22ce);
    border-radius: 0 2px 2px 0;
}

.buff-item:hover {
    transform: translateX(4px);
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.9) 0%, rgba(71, 85, 105, 0.9) 100%);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.buff-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(126, 34, 206, 0.2) 100%);
    border-radius: 14px;
    margin-left: 16px;
    border: 1px solid rgba(147, 51, 234, 0.3);
}

.buff-icon i {
    font-size: 22px;
    background: linear-gradient(45deg, #9333ea, #7e22ce);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.buff-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.buff-name {
    font-size: 15px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.buff-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.buff-time {
    font-size: 13px;
    color: #94a3b8;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
}

.buff-time i {
    font-size: 12px;
    color: #64748b;
}

.buff-multiplier {
    font-size: 14px;
    font-weight: 700;
    background: linear-gradient(45deg, #f59e0b, #d97706);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding: 4px 12px;
    background-color: rgba(245, 158, 11, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1400px) {
    .activities-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .activities-panel,
    .buffs-panel {
        min-width: 320px;
    }
}

@media (max-width: 1200px) {
    .player-info-panel {
        left: 40px;
        min-width: 300px;
    }

    .activities-panel,
    .buffs-panel {
        right: 20px;
        min-width: 300px;
    }

    .activities-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 900px) {
    .player-count {
        top: 20px;
        left: 20px;
        padding: 12px 18px;
        font-size: 16px;
    }

    .player-info-panel {
        left: 20px;
        min-width: 280px;
    }

    .activities-panel,
    .buffs-panel {
        right: 15px;
        min-width: 280px;
    }

    .panel-header,
    .buffs-header {
        padding: 16px 20px;
        font-size: 14px;
    }

    .info-item {
        padding: 12px 20px;
    }

    .activity-item {
        padding: 16px 10px;
        min-height: 80px;
    }

    .activity-icon {
        font-size: 20px;
    }

    .activity-label {
        font-size: 10px;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-gradient-primary {
    background: linear-gradient(45deg, #dc3545, #b91c1c);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-gradient-secondary {
    background: linear-gradient(45deg, #3b82f6, #2563eb);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-gradient-success {
    background: linear-gradient(45deg, #22c55e, #15803d);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-gradient-warning {
    background: linear-gradient(45deg, #f59e0b, #d97706);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-gradient-purple {
    background: linear-gradient(45deg, #9333ea, #7e22ce);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* ===== SCROLLBAR STYLING ===== */
.buffs-list::-webkit-scrollbar {
    width: 6px;
}

.buffs-list::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 3px;
}

.buffs-list::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #9333ea, #7e22ce);
    border-radius: 3px;
}

.buffs-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #a855f7, #9333ea);
}

/* ===== LOADING STATES ===== */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
