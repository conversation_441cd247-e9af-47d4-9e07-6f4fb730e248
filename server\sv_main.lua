local QBCore = exports["qb-core"]:GetCoreObject()

-- Player Count Callback
QBCore.Functions.CreateCallback('ws-playerinfo:getPlayerCount', function(source, cb)
    local playerCount = #GetPlayers()
    cb(playerCount)
end)

-- Activities Status Callback
QBCore.Functions.CreateCallback('ws-playerinfo:getActivitiesStatus', function(source, cb)
    local activities = {
        ['leo-kidnap'] = 'available',  -- available, busy, unavailable
        ['stores'] = 'available',
        ['police'] = 'available',
        ['sheriff'] = 'available',
        ['houses'] = 'busy',
        ['exchange'] = 'available',
        ['hospital'] = 'available',
        ['jewelary'] = 'available'
    }

    -- Here you can add logic to check actual activity statuses
    -- For example, check if there are enough police online, etc.

    cb(activities)
end)

-- Active Buffs Callback
QBCore.Functions.CreateCallback('ws-playerinfo:getActiveBuffs', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then
        cb({})
        return
    end

    -- Sample buffs data - replace with your actual buff system
    local buffs = {
        {
            name = "Post OP",
            icon = "fa-duotone fa-truck-fast",
            timeLeft = "15m",
            multiplier = "3"
        },
        {
            name = "Uncle Damby's Farms",
            icon = "fa-duotone fa-wheat",
            timeLeft = "6m",
            multiplier = "2"
        }
    }

    -- Here you can add logic to get actual active buffs from your system

    cb(buffs)
end)

-- Function to get job availability (example)
local function GetJobAvailability(jobName)
    local onlineCount = 0
    local Players = QBCore.Functions.GetQBPlayers()

    for k, v in pairs(Players) do
        if v.PlayerData.job.name == jobName and v.PlayerData.job.onduty then
            onlineCount = onlineCount + 1
        end
    end

    return onlineCount
end

-- Function to check if activity is available
local function IsActivityAvailable(activityName)
    -- Add your logic here to check if specific activities are available
    -- For example, check cooldowns, player counts, etc.

    if activityName == 'police' or activityName == 'sheriff' then
        local policeCount = GetJobAvailability('police')
        local sheriffCount = GetJobAvailability('sheriff')
        return (policeCount + sheriffCount) >= 2 -- Require at least 2 law enforcement
    end

    return true -- Default to available
end
